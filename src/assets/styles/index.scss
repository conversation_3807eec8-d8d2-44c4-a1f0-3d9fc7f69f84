// 导入变量
@use "./variables.scss";

// 导入重置样式
@use "./reset.scss";

#app,
html,
body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

// 修改默认滚动条样式
body {
  overflow-y: auto;
}


.hero-network {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  .network-node {
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--neon-cyan);
    border-radius: 50%;
    box-shadow: 0 0 20px var(--neon-cyan);

    &.node-1 {
      top: 25%;
      left: 20%;
      animation: pulse 3s infinite 0s;
    }
    &.node-2 {
      top: 40%;
      left: 75%;
      animation: pulse 3s infinite 0.5s;
    }
    &.node-3 {
      top: 65%;
      left: 30%;
      animation: pulse 3s infinite 1s;
    }
    &.node-4 {
      top: 80%;
      left: 80%;
      animation: pulse 3s infinite 1.5s;
    }
    &.node-5 {
      top: 15%;
      left: 85%;
      animation: pulse 3s infinite 2s;
    }
    &.node-6 {
      top: 55%;
      left: 10%;
      animation: pulse 3s infinite 2.5s;
    }
    &.node-7 {
      top: 35%;
      left: 50%;
      animation: pulse 3s infinite 1.2s;
    }
    &.node-8 {
      top: 70%;
      left: 65%;
      animation: pulse 3s infinite 1.8s;
    }
  }

  .network-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;

    .network-path {
      fill: none;
      stroke: var(--electric-blue);
      stroke-width: 0.5;
      stroke-dasharray: 5, 5;
      animation: dash 20s linear infinite;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

@keyframes dash {
  to {
    stroke-dashoffset: -100;
  }
}
